Buatkan saya aplikasi Android WebView menggunakan JavaScript dengan Apache Cordova. 
Aplikasi ini harus otomatis membuka URL https://www.youtube.com/ saat dijalankan.

Spesifikasi:
1. Framework: Apache Cordova (versi terbaru).
2. Saat aplikasi dibuka, langsung tampil halaman https://www.youtube.com/.
3. Tambahkan kontrol navigasi sederhana: tombol Back, Forward, dan Reload.
4. Support fullscreen video YouTube.
5. Sertakan struktur project (config.xml, index.html, js).
6. Sertakan instruksi build ke file APK menggunakan `cordova build android`.
7. Output akhir: kode lengkap index.html dengan <iframe> atau WebView bawaan Cordova yang menampilkan YouTube.

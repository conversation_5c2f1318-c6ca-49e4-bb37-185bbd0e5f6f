// YouTube WebView App - Main JavaScript File

class YouTubeWebViewApp {
    constructor() {
        this.webview = null;
        this.backBtn = null;
        this.forwardBtn = null;
        this.reloadBtn = null;
        this.currentUrl = null;
        this.loadingIndicator = null;
        this.history = ['https://www.youtube.com/'];
        this.currentHistoryIndex = 0;
        
        // Bind methods
        this.onDeviceReady = this.onDeviceReady.bind(this);
        this.goBack = this.goBack.bind(this);
        this.goForward = this.goForward.bind(this);
        this.reload = this.reload.bind(this);
        this.onWebViewLoad = this.onWebViewLoad.bind(this);
    }

    init() {
        document.addEventListener('deviceready', this.onDeviceReady, false);
        
        // Fallback for browser testing
        if (typeof cordova === 'undefined') {
            console.log('Running in browser mode');
            setTimeout(this.onDeviceReady, 100);
        }
    }

    onDeviceReady() {
        console.log('Device is ready');
        this.setupElements();
        this.setupEventListeners();
        this.loadYouTube();
        this.hideLoadingIndicator();
    }

    setupElements() {
        this.webview = document.getElementById('webview');
        this.backBtn = document.getElementById('backBtn');
        this.forwardBtn = document.getElementById('forwardBtn');
        this.reloadBtn = document.getElementById('reloadBtn');
        this.currentUrl = document.getElementById('currentUrl');
        this.loadingIndicator = document.getElementById('loadingIndicator');
    }

    setupEventListeners() {
        // Navigation button events
        this.backBtn.addEventListener('click', this.goBack);
        this.forwardBtn.addEventListener('click', this.goForward);
        this.reloadBtn.addEventListener('click', this.reload);

        // WebView events
        this.webview.addEventListener('load', this.onWebViewLoad);
        
        // Handle Android back button
        document.addEventListener('backbutton', this.handleBackButton.bind(this), false);
        
        // Handle orientation change
        window.addEventListener('orientationchange', this.handleOrientationChange.bind(this));
        
        // Handle network status
        document.addEventListener('online', this.onOnline.bind(this), false);
        document.addEventListener('offline', this.onOffline.bind(this), false);
    }

    loadYouTube() {
        console.log('Loading YouTube...');
        this.showLoadingIndicator();
        
        // Set the iframe source to YouTube
        this.webview.src = 'https://www.youtube.com/';
        this.updateUrlDisplay('youtube.com');
        
        // Hide loading after a delay
        setTimeout(() => {
            this.hideLoadingIndicator();
        }, 3000);
    }

    goBack() {
        if (this.currentHistoryIndex > 0) {
            this.currentHistoryIndex--;
            const url = this.history[this.currentHistoryIndex];
            this.webview.src = url;
            this.updateUrlDisplay(this.extractDomain(url));
            this.updateNavigationButtons();
        }
    }

    goForward() {
        if (this.currentHistoryIndex < this.history.length - 1) {
            this.currentHistoryIndex++;
            const url = this.history[this.currentHistoryIndex];
            this.webview.src = url;
            this.updateUrlDisplay(this.extractDomain(url));
            this.updateNavigationButtons();
        }
    }

    reload() {
        console.log('Reloading page...');
        this.showLoadingIndicator();
        
        // Reload the iframe
        const currentSrc = this.webview.src;
        this.webview.src = '';
        setTimeout(() => {
            this.webview.src = currentSrc;
            setTimeout(() => {
                this.hideLoadingIndicator();
            }, 2000);
        }, 100);
    }

    onWebViewLoad() {
        console.log('WebView loaded');
        this.hideLoadingIndicator();
    }

    updateUrlDisplay(domain) {
        if (this.currentUrl) {
            this.currentUrl.textContent = domain;
        }
    }

    updateNavigationButtons() {
        // Update back button state
        if (this.backBtn) {
            this.backBtn.disabled = this.currentHistoryIndex <= 0;
        }
        
        // Update forward button state
        if (this.forwardBtn) {
            this.forwardBtn.disabled = this.currentHistoryIndex >= this.history.length - 1;
        }
    }

    extractDomain(url) {
        try {
            const domain = new URL(url).hostname;
            return domain.replace('www.', '');
        } catch (e) {
            return url;
        }
    }

    showLoadingIndicator() {
        if (this.loadingIndicator) {
            this.loadingIndicator.classList.remove('hidden');
        }
    }

    hideLoadingIndicator() {
        if (this.loadingIndicator) {
            this.loadingIndicator.classList.add('hidden');
        }
    }

    handleBackButton() {
        // Handle Android hardware back button
        if (this.currentHistoryIndex > 0) {
            this.goBack();
        } else {
            // Exit app if at the beginning of history
            if (navigator.app) {
                navigator.app.exitApp();
            }
        }
    }

    handleOrientationChange() {
        // Handle orientation changes
        setTimeout(() => {
            console.log('Orientation changed');
            // Force a small reload to adjust iframe
            if (this.webview) {
                const currentSrc = this.webview.src;
                this.webview.style.height = '0px';
                setTimeout(() => {
                    this.webview.style.height = '100%';
                }, 100);
            }
        }, 500);
    }

    onOnline() {
        console.log('Device is online');
        this.updateUrlDisplay('youtube.com (online)');
        setTimeout(() => {
            this.updateUrlDisplay('youtube.com');
        }, 2000);
    }

    onOffline() {
        console.log('Device is offline');
        this.updateUrlDisplay('youtube.com (offline)');
    }
}

// Initialize the app
const app = new YouTubeWebViewApp();

// Start the app when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        app.init();
    });
} else {
    app.init();
}

// Global error handler
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
});

// Export for potential external use
window.YouTubeWebViewApp = app;

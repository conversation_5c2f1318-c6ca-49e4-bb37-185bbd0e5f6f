{"name": "youtube-webview-app", "displayName": "YouTube WebView", "version": "1.0.0", "description": "A simple YouTube WebView application built with Apache Cordova", "main": "index.js", "scripts": {"build": "<PERSON>ova build android", "build-release": "cordova build android --release", "run": "cordova run android", "emulate": "<PERSON>ova emulate android", "serve": "cordova serve", "clean": "cordova clean android", "prepare": "cordova prepare android", "requirements": "cordova requirements android", "platform-add": "cordova platform add android", "plugin-install": "npm run install-plugins", "install-plugins": "cordova plugin add cordova-plugin-whitelist cordova-plugin-inappbrowser cordova-plugin-device cordova-plugin-splashscreen cordova-plugin-statusbar"}, "keywords": ["<PERSON><PERSON>", "android", "webview", "youtube", "mobile", "app"], "author": "Developer Team", "license": "MIT", "devDependencies": {"cordova": "^12.0.0", "cordova-android": "^12.0.0"}, "cordova": {"platforms": ["android"], "plugins": {"cordova-plugin-whitelist": {}, "cordova-plugin-inappbrowser": {}, "cordova-plugin-device": {}, "cordova-plugin-splashscreen": {}, "cordova-plugin-statusbar": {}}}, "repository": {"type": "git", "url": "https://github.com/example/youtube-webview-app.git"}, "bugs": {"url": "https://github.com/example/youtube-webview-app/issues"}, "homepage": "https://github.com/example/youtube-webview-app#readme"}
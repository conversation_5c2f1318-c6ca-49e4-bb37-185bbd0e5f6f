/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #000;
    color: #fff;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
}

/* Navigation Bar */
.navigation-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: linear-gradient(135deg, #ff0000, #cc0000);
    display: flex;
    align-items: center;
    padding: 0 10px;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Navigation Buttons */
.nav-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    width: 40px;
    height: 35px;
    margin-right: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.nav-btn:active {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(0.95);
}

.nav-btn:disabled {
    background: rgba(255, 255, 255, 0.1);
    cursor: not-allowed;
    opacity: 0.5;
}

.nav-icon {
    color: #fff;
    font-size: 18px;
    font-weight: bold;
}

/* URL Display */
.url-display {
    flex: 1;
    margin-left: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 14px;
    color: #fff;
    text-align: center;
}

#currentUrl {
    opacity: 0.9;
}

/* WebView Container */
.webview-container {
    position: absolute;
    top: 50px;
    left: 0;
    right: 0;
    bottom: 0;
    background: #000;
}

#webview {
    width: 100%;
    height: 100%;
    border: none;
    background: #000;
}

/* Loading Indicator */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 999;
    background: rgba(0, 0, 0, 0.8);
    padding: 30px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.loading-indicator.hidden {
    display: none;
}

/* Spinner Animation */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ff0000;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-indicator p {
    color: #fff;
    font-size: 16px;
    margin: 0;
}

/* Mobile Responsive */
@media screen and (max-width: 480px) {
    .navigation-bar {
        height: 45px;
        padding: 0 8px;
    }
    
    .nav-btn {
        width: 35px;
        height: 30px;
        margin-right: 6px;
    }
    
    .nav-icon {
        font-size: 16px;
    }
    
    .url-display {
        font-size: 12px;
        padding: 6px 12px;
    }
    
    .webview-container {
        top: 45px;
    }
}

/* Landscape Mode */
@media screen and (orientation: landscape) {
    .navigation-bar {
        height: 40px;
    }
    
    .webview-container {
        top: 40px;
    }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #000;
    }
}

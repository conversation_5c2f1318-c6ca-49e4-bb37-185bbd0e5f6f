# YouTube WebView App

Aplikasi Android WebView sederhana yang menampilkan YouTube menggunakan Apache Cordova.

## 📱 Features

- ✅ Otomatis membuka YouTube saat aplikasi dijalankan
- ✅ Kontrol navigasi: Back, Forward, dan Reload
- ✅ Support fullscreen video YouTube
- ✅ Responsive design untuk berbagai ukuran layar
- ✅ Handle hardware back button Android
- ✅ Loading indicator
- ✅ Network status detection (online/offline)
- ✅ Orientation change support

## 🚀 Quick Start

### Prerequisites
- Node.js (v14+)
- Apache Cordova CLI
- Android Studio & Android SDK
- Java Development Kit (JDK 8/11)

### Installation
```bash
# Clone atau download project ini
git clone <repository-url>
cd youtube-webview-app

# Install Cordova CLI (jika belum ada)
npm install -g cordova

# Add Android platform
cordova platform add android

# Install plugins
npm run install-plugins

# Build APK
npm run build
```

### Build Commands
```bash
# Development build
npm run build

# Production build
npm run build-release

# Run on device/emulator
npm run run

# Test in browser
npm run serve
```

## 📁 Project Structure

```
youtube-webview-app/
├── config.xml              # Konfigurasi Cordova
├── package.json            # Dependencies & scripts
├── BUILD_INSTRUCTIONS.md   # Instruksi build lengkap
├── README.md              # Dokumentasi project
└── www/                   # Source code aplikasi
    ├── index.html         # Main HTML file
    ├── css/
    │   └── index.css      # Styling
    └── js/
        └── index.js       # JavaScript logic
```

## 🎯 How It Works

1. **Startup**: Aplikasi otomatis load YouTube di iframe
2. **Navigation**: Tombol Back/Forward menggunakan history management
3. **Reload**: Refresh iframe untuk reload halaman
4. **Fullscreen**: Support native fullscreen video YouTube
5. **Mobile Optimized**: Responsive design dengan touch-friendly controls

## 🔧 Configuration

### config.xml
- App ID: `com.example.youtubewebview`
- Min SDK: Android 5.1 (API 22)
- Target SDK: Android 13 (API 33)
- Permissions: Internet, Network State, WiFi State

### Plugins Used
- `cordova-plugin-whitelist`: Security whitelist
- `cordova-plugin-inappbrowser`: Enhanced browser features
- `cordova-plugin-device`: Device information
- `cordova-plugin-splashscreen`: Splash screen support
- `cordova-plugin-statusbar`: Status bar control

## 📱 Screenshots

*Navigation Bar dengan tombol Back, Forward, Reload*
- Tombol navigasi dengan ikon yang jelas
- URL display menunjukkan domain saat ini
- Design mengikuti tema YouTube (merah)

*WebView Container*
- Fullscreen iframe untuk YouTube
- Support video fullscreen native
- Responsive untuk portrait/landscape

## 🛠️ Development

### Browser Testing
```bash
npm run serve
# Buka http://localhost:8000
```

### Device Testing
```bash
# Connect Android device dengan USB debugging
npm run run

# Atau gunakan emulator
npm run emulate
```

### Debugging
- Chrome DevTools untuk browser testing
- Android Studio Logcat untuk device debugging
- `cordova run android --verbose` untuk detailed logs

## 📋 Build Output

Setelah build berhasil:
- **Debug APK**: `platforms/android/app/build/outputs/apk/debug/app-debug.apk`
- **Release APK**: `platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk`

## 🔍 Troubleshooting

### Common Issues:
1. **Gradle build failed**: Run `cordova clean android` then rebuild
2. **SDK not found**: Check `ANDROID_HOME` environment variable
3. **Plugin errors**: Remove and re-add Android platform
4. **Network issues**: Check internet connection and CORS settings

### Verification:
```bash
cordova requirements android  # Check system requirements
cordova info                 # Show environment info
adb devices                  # Check connected devices
```

## 📄 License

MIT License - feel free to use and modify.

## 🤝 Contributing

1. Fork the project
2. Create feature branch
3. Commit changes
4. Push to branch
5. Open Pull Request

## 📞 Support

Jika ada pertanyaan atau issues, silakan buat issue di repository ini.

---

**Happy Coding!** 🎉

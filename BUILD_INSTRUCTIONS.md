# YouTube WebView App - Build Instructions

## Prerequisites

Se<PERSON><PERSON> memulai, pastikan <PERSON>a telah menginstall:

1. **Node.js** (versi 14 atau lebih baru)
   - Download dari: https://nodejs.org/
   - Verifikasi: `node --version` dan `npm --version`

2. **Apache Cordova CLI**
   ```bash
   npm install -g cordova
   ```
   - Verifikasi: `cordova --version`

3. **Android Studio** dan **Android SDK**
   - Download dari: https://developer.android.com/studio
   - Install Android SDK Tools, Platform Tools, dan Build Tools
   - Set environment variables:
     - `ANDROID_HOME` = path ke Android SDK
     - `JAVA_HOME` = path ke JDK

4. **Java Development Kit (JDK) 8 atau 11**
   - Verifikasi: `java -version` dan `javac -version`

## Setup Project

### 1. Inisialisasi Project Cordova
```bash
# Buat project baru (jika belum ada)
cordova create youtube-webview com.example.youtubewebview "YouTube WebView"
cd youtube-webview

# Atau gunakan project yang sudah ada
cd path/to/your/project
```

### 2. Copy Files
Pastikan struktur folder seperti ini:
```
youtube-webview/
├── config.xml
├── www/
│   ├── index.html
│   ├── css/
│   │   └── index.css
│   └── js/
│       └── index.js
└── BUILD_INSTRUCTIONS.md
```

### 3. Add Android Platform
```bash
cordova platform add android
```

### 4. Install Required Plugins
```bash
cordova plugin add cordova-plugin-whitelist
cordova plugin add cordova-plugin-inappbrowser
cordova plugin add cordova-plugin-device
cordova plugin add cordova-plugin-splashscreen
cordova plugin add cordova-plugin-statusbar
```

## Build Process

### 1. Build untuk Development
```bash
# Build debug APK
cordova build android

# Atau build dengan verbose output
cordova build android --verbose
```

### 2. Build untuk Production
```bash
# Build release APK (unsigned)
cordova build android --release

# Build release APK dengan signing (opsional)
cordova build android --release -- --keystore="path/to/keystore.jks" --storePassword="password" --alias="alias_name" --password="key_password"
```

### 3. Run di Device/Emulator
```bash
# Run di emulator
cordova emulate android

# Run di device yang terhubung
cordova run android

# Run dengan live reload (development)
cordova run android --livereload
```

## Output Locations

Setelah build berhasil, APK akan tersedia di:

- **Debug APK**: `platforms/android/app/build/outputs/apk/debug/app-debug.apk`
- **Release APK**: `platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk`

## Troubleshooting

### Common Issues:

1. **Gradle Build Failed**
   ```bash
   # Clean dan rebuild
   cordova clean android
   cordova build android
   ```

2. **SDK Not Found**
   - Pastikan `ANDROID_HOME` dan `JAVA_HOME` sudah di-set
   - Restart terminal/command prompt

3. **Plugin Issues**
   ```bash
   # Remove dan add ulang platform
   cordova platform remove android
   cordova platform add android
   ```

4. **Network/CORS Issues**
   - Pastikan `config.xml` memiliki `<access origin="*" />`
   - Check Content Security Policy di `index.html`

### Verification Commands:
```bash
# Check Cordova requirements
cordova requirements android

# List installed platforms
cordova platform list

# List installed plugins
cordova plugin list

# Check Cordova info
cordova info
```

## Testing

### 1. Browser Testing (Development)
```bash
# Serve di browser untuk testing cepat
cordova serve
# Buka http://localhost:8000
```

### 2. Device Testing
```bash
# Install APK manual
adb install platforms/android/app/build/outputs/apk/debug/app-debug.apk

# Check device connection
adb devices
```

## Additional Notes

- **Minimum Android Version**: API Level 22 (Android 5.1)
- **Target Android Version**: API Level 33 (Android 13)
- **Permissions**: Internet, Network State, WiFi State
- **Features**: Fullscreen video support, Hardware back button handling

## File Structure Explanation

- `config.xml`: Konfigurasi utama aplikasi Cordova
- `www/index.html`: Interface utama dengan iframe YouTube
- `www/css/index.css`: Styling untuk navigation dan layout
- `www/js/index.js`: Logic untuk navigation controls dan WebView handling

## Performance Tips

1. **Optimize for Mobile**: App sudah dioptimasi untuk mobile dengan responsive design
2. **Network Handling**: App mendeteksi status online/offline
3. **Memory Management**: Iframe reload untuk mencegah memory leaks
4. **Orientation Support**: Auto-adjust saat rotasi device

Selamat mencoba! 🚀

<?xml version='1.0' encoding='utf-8'?>
<widget id="com.example.youtubewebview" version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <name>YouTube WebView</name>
    <description>
        A simple YouTube WebView application built with Apache Cordova
    </description>
    <author email="<EMAIL>" href="http://example.com">
        Developer Team
    </author>
    <content src="index.html" />
    
    <!-- Permissions -->
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    
    <!-- Platform specific configurations -->
    <platform name="android">
        <allow-intent href="market:*" />
        
        <!-- Android specific preferences -->
        <preference name="android-minSdkVersion" value="22" />
        <preference name="android-targetSdkVersion" value="33" />
        
        <!-- WebView preferences -->
        <preference name="AllowInlineMediaPlayback" value="true" />
        <preference name="MediaPlaybackRequiresUserAction" value="false" />
        <preference name="BackupWebStorage" value="cloud" />
        
        <!-- Fullscreen support -->
        <preference name="Fullscreen" value="false" />
        <preference name="Orientation" value="default" />
        
        <!-- Network security -->
        <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application">
            <application android:usesCleartextTraffic="true" />
        </edit-config>
        
        <!-- Permissions for network access -->
        <uses-permission android:name="android.permission.INTERNET" />
        <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
        <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    </platform>
    
    <!-- Global preferences -->
    <preference name="DisallowOverscroll" value="true" />
    <preference name="android-minSdkVersion" value="22" />
    <preference name="BackgroundColor" value="0xff000000" />
    <preference name="HideKeyboardFormAccessoryBar" value="true" />
    <preference name="Orientation" value="default" />
    
    <!-- Plugins -->
    <plugin name="cordova-plugin-whitelist" spec="1" />
    <plugin name="cordova-plugin-inappbrowser" spec="~5.0.0" />
    <plugin name="cordova-plugin-device" spec="~2.1.0" />
    <plugin name="cordova-plugin-splashscreen" spec="~6.0.0" />
    <plugin name="cordova-plugin-statusbar" spec="~3.0.0" />
</widget>
